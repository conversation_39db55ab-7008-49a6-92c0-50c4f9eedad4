#!/usr/bin/env python3
"""
SAFit评估工具使用示例
演示如何使用SAFit工具评估您的模型
"""

import os
import sys

def example_rgb_evaluation():
    """RGB模态评估示例"""
    print("=" * 60)
    print("RGB模态SAFit评估示例")
    print("=" * 60)
    
    # 示例命令
    cmd = """python evaluate.py \\
    --model_path /path/to/your/best.pt \\
    --dataset_config example_config.yaml \\
    --modal rgb \\
    --img_size 640 512 \\
    --conf_threshold 0.001 \\
    --output_dir ./results/rgb"""
    
    print("命令示例:")
    print(cmd)
    print()
    
    # 实际可用的命令（基于您的数据集路径）
    actual_cmd = f"""python evaluate.py \\
    --model_path /path/to/your/best.pt \\
    --gt_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb \\
    --img_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb \\
    --img_size 640 512 \\
    --conf_threshold 0.001 \\
    --output_dir ./results/rgb"""
    
    print("基于您的数据集路径的实际命令:")
    print(actual_cmd)
    print()

def example_thermal_evaluation():
    """热红外模态评估示例"""
    print("=" * 60)
    print("热红外模态SAFit评估示例")
    print("=" * 60)
    
    # 实际可用的命令
    actual_cmd = f"""python evaluate.py \\
    --model_path /path/to/your/best.pt \\
    --gt_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb \\
    --img_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/thermal \\
    --img_size 640 512 \\
    --conf_threshold 0.001 \\
    --output_dir ./results/thermal"""
    
    print("热红外评估命令:")
    print(actual_cmd)
    print()

def example_batch_inference():
    """批量推理示例"""
    print("=" * 60)
    print("批量推理示例")
    print("=" * 60)
    
    cmd = f"""python model_inference.py \\
    --model_path /path/to/your/best.pt \\
    --img_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb \\
    --output_file predictions_rgb.txt \\
    --img_size 640 512 \\
    --conf_threshold 0.25"""
    
    print("批量推理命令:")
    print(cmd)
    print()
    
    print("然后使用预测文件进行评估:")
    eval_cmd = f"""python evaluate.py \\
    --model_path dummy \\
    --gt_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb \\
    --pred_file predictions_rgb.txt \\
    --img_size 640 512 \\
    --output_dir ./results/rgb_from_file"""
    
    print(eval_cmd)
    print()

def example_python_api():
    """Python API使用示例"""
    print("=" * 60)
    print("Python API使用示例")
    print("=" * 60)
    
    code_example = '''
from safit_evaluator import SAFitEvaluator

# 创建评估器
evaluator = SAFitEvaluator(
    class_names=['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']
)

# 加载地面真值
gt_dir = "/media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb"
evaluator.load_ground_truth(gt_dir, img_size=(640, 512))

# 加载预测结果（方式1：从模型）
model_path = "/path/to/your/best.pt"
img_dir = "/media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb"
evaluator.load_predictions_from_model(model_path, img_dir, conf_threshold=0.001)

# 或者加载预测结果（方式2：从文件）
# evaluator.load_predictions_from_file("predictions.txt")

# 运行评估
evaluator.evaluate()
evaluator.accumulate()
stats = evaluator.summarize()

# 获取结果
results = evaluator.get_results_dict()
print(f"AP (SAFit): {results['AP']:.3f}")
print(f"AP50: {results['AP50']:.3f}")
print(f"AP_tiny: {results['AP_tiny']:.3f}")

# 保存结果
evaluator.save_results("safit_results.txt")
'''
    
    print("Python代码示例:")
    print(code_example)

def show_file_structure():
    """显示文件结构说明"""
    print("=" * 60)
    print("文件结构说明")
    print("=" * 60)
    
    structure = """
SAFit/
├── __init__.py              # Python包初始化文件
├── safit_utils.py           # SAFit核心计算函数
├── safit_evaluator.py       # SAFit评估器主类
├── model_inference.py       # 模型推理工具
├── evaluate.py              # 命令行评估脚本
├── test_safit.py           # 测试脚本
├── usage_example.py        # 使用示例（本文件）
├── example_config.yaml     # 数据集配置示例
└── README.md               # 详细说明文档

使用前准备：
1. 确保有训练好的模型文件 (best.pt)
2. 准备YOLO格式的地面真值标签
3. 准备测试图像
4. 安装必要的依赖包
"""
    
    print(structure)

def main():
    """主函数"""
    print("🎯 SAFit评估工具使用指南")
    print("基于RGBT-Tiny项目，专门用于小目标检测的SAFit指标评估")
    print()
    
    show_file_structure()
    example_rgb_evaluation()
    example_thermal_evaluation()
    example_batch_inference()
    example_python_api()
    
    print("=" * 60)
    print("📋 快速开始步骤")
    print("=" * 60)
    print("1. 将您的best.pt模型文件路径替换到上述命令中")
    print("2. 确认数据集路径正确")
    print("3. 运行评估命令")
    print("4. 查看results目录中的评估结果")
    print()
    print("💡 提示:")
    print("- 使用较低的置信度阈值(0.001)以获得完整评估")
    print("- SAFit指标对小目标更敏感，适合小目标检测评估")
    print("- 支持RGB、热红外和双模态评估")
    print("- 结果包含不同尺度目标的详细性能分析")
    print()
    print("❓ 如有问题，请查看README.md或运行test_safit.py进行测试")

if __name__ == '__main__':
    main()
