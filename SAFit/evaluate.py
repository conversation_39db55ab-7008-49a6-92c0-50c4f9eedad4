#!/usr/bin/env python3
"""
SAFit Evaluation Script
Usage: python evaluate.py --model_path best.pt --gt_dir /path/to/gt/labels --img_dir /path/to/images
"""

import argparse
import os
import sys
import yaml

# Add current directory to path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from safit_evaluator import SAFitEvaluator

def parse_args():
    parser = argparse.ArgumentParser(description='SAFit Evaluation Tool')
    
    # Required arguments
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to trained model (best.pt)')
    parser.add_argument('--gt_dir', type=str, required=True,
                        help='Directory containing ground truth YOLO format labels')
    
    # Optional arguments
    parser.add_argument('--img_dir', type=str, default=None,
                        help='Directory containing test images (for model inference)')
    parser.add_argument('--pred_file', type=str, default=None,
                        help='Text file containing predictions (alternative to model inference)')
    parser.add_argument('--img_size', type=int, nargs=2, default=[640, 512],
                        help='Image size (width height)')
    parser.add_argument('--conf_threshold', type=float, default=0.001,
                        help='Confidence threshold for predictions')
    parser.add_argument('--output_dir', type=str, default='./results',
                        help='Output directory for results')
    parser.add_argument('--dataset_config', type=str, default=None,
                        help='Dataset configuration YAML file')
    parser.add_argument('--modal', type=str, choices=['rgb', 'thermal', 'both'], default='rgb',
                        help='Evaluation modality')
    
    return parser.parse_args()

def load_dataset_config(config_path):
    """Load dataset configuration from YAML file"""
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"Error loading dataset config: {e}")
        return None

def main():
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load dataset configuration if provided
    if args.dataset_config:
        config = load_dataset_config(args.dataset_config)
        if config:
            print(f"Loaded dataset config from {args.dataset_config}")
            class_names = config.get('names', ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship'])
            
            # Update paths based on modality
            if args.modal == 'rgb':
                if 'val_rgb' in config:
                    img_dir = config['val_rgb']
                else:
                    img_dir = args.img_dir
                gt_dir = config.get('val_labels', args.gt_dir)
            elif args.modal == 'thermal':
                if 'val_ir' in config:
                    img_dir = config['val_ir']
                else:
                    img_dir = args.img_dir
                gt_dir = config.get('val_labels', args.gt_dir)
            else:  # both
                img_dir = args.img_dir
                gt_dir = args.gt_dir
        else:
            class_names = ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']
            img_dir = args.img_dir
            gt_dir = args.gt_dir
    else:
        class_names = ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']
        img_dir = args.img_dir
        gt_dir = args.gt_dir
    
    print("SAFit Evaluation Tool")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Ground Truth: {gt_dir}")
    print(f"Images: {img_dir}")
    print(f"Modality: {args.modal}")
    print(f"Image Size: {args.img_size}")
    print(f"Classes: {class_names}")
    print(f"Output: {args.output_dir}")
    print("=" * 50)
    
    # Initialize evaluator
    evaluator = SAFitEvaluator(class_names=class_names)
    
    # Load ground truth
    print("Loading ground truth...")
    evaluator.load_ground_truth(gt_dir, img_size=tuple(args.img_size))
    print(f"Loaded {len(evaluator.gt_data)} ground truth files")
    
    # Load predictions
    if args.pred_file:
        print(f"Loading predictions from file: {args.pred_file}")
        evaluator.load_predictions_from_file(args.pred_file)
    elif img_dir and os.path.exists(args.model_path):
        print(f"Loading predictions from model: {args.model_path}")
        evaluator.load_predictions_from_model(args.model_path, img_dir, args.conf_threshold)
    else:
        print("Error: Either --img_dir or --pred_file must be provided")
        return
    
    print(f"Loaded predictions for {len(evaluator.pred_data)} images")
    
    # Run evaluation
    print("\nRunning evaluation...")
    evaluator.evaluate()
    
    print("Accumulating results...")
    evaluator.accumulate()
    
    print("Computing summary statistics...")
    stats = evaluator.summarize()
    
    # Save results
    results_file = os.path.join(args.output_dir, f'safit_results_{args.modal}.txt')
    evaluator.save_results(results_file)
    
    # Save detailed results as numpy file
    import numpy as np
    detailed_results_file = os.path.join(args.output_dir, f'safit_detailed_{args.modal}.npz')
    np.savez(detailed_results_file, 
             precision=evaluator.eval_results['precision'],
             recall=evaluator.eval_results['recall'],
             scores=evaluator.eval_results['scores'],
             stats=stats,
             params=evaluator.eval_results['params'])
    
    print(f"\nEvaluation completed!")
    print(f"Results saved to: {results_file}")
    print(f"Detailed results saved to: {detailed_results_file}")
    
    # Print summary
    results_dict = evaluator.get_results_dict()
    print(f"\nSummary (SAFit metric):")
    print(f"AP (0.5:0.95): {results_dict['AP']:.3f}")
    print(f"AP50: {results_dict['AP50']:.3f}")
    print(f"AP75: {results_dict['AP75']:.3f}")
    print(f"AP tiny: {results_dict['AP_tiny']:.3f}")
    print(f"AP small: {results_dict['AP_small']:.3f}")

if __name__ == '__main__':
    main()
