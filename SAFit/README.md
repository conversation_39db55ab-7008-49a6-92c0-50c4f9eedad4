# SAFit Evaluation Tool

这是一个基于RGBT-Tiny项目的SAFit评估工具，专门用于小目标检测的性能评估。

## 功能特点

- **SAFit指标评估**: 使用SAFit指标替代传统IoU进行评估，对小目标更加敏感
- **多模态支持**: 支持RGB、热红外和双模态融合评估
- **融合算法支持**: 支持单模态和可见光-红外融合算法
- **多种模型格式**: 支持YOLOv5、YOLOv8等多种模型格式
- **YOLO格式兼容**: 支持标准YOLO格式的归一化标签
- **详细统计**: 提供不同尺度目标的详细性能统计
- **灵活推理**: 自动检测和适配不同的融合模型架构

## 安装依赖

```bash
# 基础依赖
pip install numpy opencv-python pyyaml

# PyTorch (用于模型推理)
pip install torch torchvision

# 可选: YOLOv5/YOLOv8支持
pip install yolov5
pip install ultralytics
```

## 使用方法

### 1. 单模态评估

```bash
# RGB模态评估
python evaluate.py \
    --model_path /path/to/your/best.pt \
    --gt_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb \
    --img_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb \
    --modal rgb \
    --img_size 640 512 \
    --conf_threshold 0.001 \
    --output_dir ./results/rgb

# 热红外模态评估
python evaluate.py \
    --model_path /path/to/your/best.pt \
    --gt_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb \
    --img_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/thermal \
    --modal thermal \
    --img_size 640 512 \
    --conf_threshold 0.001 \
    --output_dir ./results/thermal
```

### 1.5. 双模态融合评估

```bash
# RGB-热红外融合算法评估
python evaluate.py \
    --model_path /path/to/your/fusion_best.pt \
    --gt_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb \
    --img_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb \
    --img_dir_thermal /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/thermal \
    --modal fusion \
    --fusion_mode fusion \
    --img_size 640 512 \
    --conf_threshold 0.001 \
    --output_dir ./results/fusion
```

### 2. 使用数据集配置文件

```bash
python evaluate.py \
    --model_path /path/to/best.pt \
    --dataset_config /path/to/dataset.yaml \
    --modal rgb \
    --output_dir ./results
```

### 3. 使用预生成的预测文件

```bash
python evaluate.py \
    --model_path dummy \
    --gt_dir /path/to/gt/labels \
    --pred_file /path/to/predictions.txt \
    --output_dir ./results
```

### 4. 批量推理

#### 单模态推理
```bash
python model_inference.py \
    --model_path /path/to/best.pt \
    --img_dir /path/to/test/images \
    --output_file predictions.txt \
    --conf_threshold 0.25
```

#### 双模态融合推理
```bash
python fusion_inference.py \
    --model_path /path/to/your/fusion_best.pt \
    --rgb_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb \
    --thermal_dir /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/thermal \
    --output_file fusion_predictions.txt \
    --img_size 640 512 \
    --conf_threshold 0.25
```

## 文件格式

### 1. 地面真值标签 (YOLO格式)

每个图像对应一个`.txt`文件，格式为：
```
class_id center_x center_y width height
```
其中坐标都是归一化的 (0-1)。

### 2. 预测文件格式

```
img_id class_id confidence x1 y1 x2 y2
```
其中坐标是绝对像素坐标。

### 3. 数据集配置文件 (YAML)

```yaml
# RGBT-Tiny 数据集配置示例
path: /path/to/dataset

# RGB模态路径
train_rgb: /path/to/train/rgb
val_rgb: /path/to/val/rgb

# 热红外模态路径  
train_ir: /path/to/train/thermal
val_ir: /path/to/val/thermal

# 标签路径
train_labels: /path/to/train/labels
val_labels: /path/to/val/labels

# 类别数量和名称
nc: 7
names: ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']
```

## 输出结果

### 1. 文本结果文件

包含详细的AP和AR指标：
- AP (0.5:0.95): 平均精度
- AP50/AP75: 特定阈值下的AP
- AP_tiny/AP_small等: 不同尺度目标的AP
- AR1/AR10/AR100: 不同最大检测数下的AR

### 2. 详细结果文件 (.npz)

包含完整的precision/recall曲线和原始数据，可用于进一步分析。

## 参数说明

- `--model_path`: 训练好的模型文件路径 (best.pt)
- `--gt_dir`: 地面真值标签目录
- `--img_dir`: 测试图像目录
- `--pred_file`: 预测结果文件 (可选，替代模型推理)
- `--img_size`: 图像尺寸 [width, height]
- `--conf_threshold`: 置信度阈值
- `--modal`: 评估模态 (rgb/thermal/fusion)
- `--fusion_mode`: 推理模式 (single/fusion)
- `--img_dir_thermal`: 热红外图像目录 (融合模式必需)
- `--output_dir`: 结果输出目录

## SAFit指标说明

SAFit (Size-Adaptive Fitness) 是专门为小目标检测设计的评估指标：

- **自适应融合**: 根据目标大小自适应地融合IoU和NWD (Normalized Wasserstein Distance)
- **小目标友好**: 对小目标的位置偏差更加敏感
- **参数C**: 控制IoU和NWD融合的阈值，默认为32

公式：
```
SAFit = sigmoid(√area - C, C) × IoU + (1 - sigmoid(√area - C, C)) × NWD
```

## 示例用法

### 评估RGB模态

```bash
python evaluate.py \
    --model_path ./runs/train/exp/weights/best.pt \
    --dataset_config ./data/rgbt_tiny.yaml \
    --modal rgb \
    --img_size 640 512 \
    --conf_threshold 0.001 \
    --output_dir ./results/rgb
```

### 评估热红外模态

```bash
python evaluate.py \
    --model_path ./runs/train/exp/weights/best.pt \
    --dataset_config ./data/rgbt_tiny.yaml \
    --modal thermal \
    --img_size 640 512 \
    --conf_threshold 0.001 \
    --output_dir ./results/thermal
```

## 注意事项

1. 确保图像尺寸参数与训练时一致
2. 地面真值标签必须是YOLO格式的归一化坐标
3. 模型文件支持YOLOv5/YOLOv8等主流格式
4. 建议使用较低的置信度阈值 (0.001) 以获得完整的评估结果

## 故障排除

1. **模型加载失败**: 检查PyTorch版本兼容性
2. **标签格式错误**: 确认标签是YOLO格式且坐标已归一化
3. **内存不足**: 减少批处理大小或使用CPU推理
4. **路径错误**: 检查所有文件路径是否正确

## 引用

如果使用此工具，请引用原始RGBT-Tiny论文：

```bibtex
@article{RGBT-Tiny,
 title = {Visible-Thermal Tiny Object Detection: A Benchmark Dataset and Baselines},
 author = {Xinyi Ying and Chao Xiao and Ruojing Li and Xu He and Boyang Li and Xu Cao and Zhaoxu Li and Yingqian Wang and Mingyuan Hu and Qingyu Xu and Zaiping Lin and Miao Li and Shilin Zhou and Wei An and Weidong Sheng and Li Liu},
 journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)},
 year = {2025},
}
```
