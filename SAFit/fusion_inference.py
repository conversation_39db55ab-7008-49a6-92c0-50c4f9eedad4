"""
Dual-Modal (RGB-Thermal) Fusion Inference Tool
Supports various fusion algorithms and model architectures
"""

import os
import numpy as np
import cv2
from pathlib import Path

def load_fusion_model(model_path):
    """
    Load fusion model from various formats
    """
    try:
        import torch
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        if model_path.endswith('.pt') or model_path.endswith('.pth'):
            try:
                model = torch.load(model_path, map_location=device)
                if isinstance(model, dict):
                    if 'model' in model:
                        model = model['model']
                    elif 'ema' in model:
                        model = model['ema']
                
                model.eval()
                
                # Detect model type based on architecture
                model_type = detect_fusion_model_type(model)
                return model, model_type
                
            except Exception as e:
                print(f"Error loading PyTorch model: {e}")
                return None, None
        
        print(f"Unsupported model format: {model_path}")
        return None, None
        
    except ImportError:
        print("PyTorch not available. Please install PyTorch to use fusion inference.")
        return None, None

def detect_fusion_model_type(model):
    """
    Detect the type of fusion model based on its architecture
    """
    model_str = str(model)
    
    # Check for common fusion architectures
    if 'dual' in model_str.lower() or 'fusion' in model_str.lower():
        return 'dual_stream'
    elif 'concat' in model_str.lower() or 'cat' in model_str.lower():
        return 'early_fusion'
    elif 'attention' in model_str.lower():
        return 'attention_fusion'
    elif hasattr(model, 'forward_dual') or hasattr(model, 'dual_inference'):
        return 'custom_dual'
    else:
        return 'unknown'

def run_fusion_inference(model, model_type, rgb_path, thermal_path, img_size=(640, 640), conf_threshold=0.25):
    """
    Run inference on RGB-Thermal image pair
    """
    try:
        if model_type == 'dual_stream':
            return run_dual_stream_inference(model, rgb_path, thermal_path, img_size, conf_threshold)
        elif model_type == 'early_fusion':
            return run_early_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold)
        elif model_type == 'attention_fusion':
            return run_attention_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold)
        elif model_type == 'custom_dual':
            return run_custom_dual_inference(model, rgb_path, thermal_path, img_size, conf_threshold)
        else:
            # Fallback: try different methods
            return run_generic_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold)
            
    except Exception as e:
        print(f"Fusion inference error: {e}")
        return []

def run_dual_stream_inference(model, rgb_path, thermal_path, img_size, conf_threshold):
    """
    Run inference for dual-stream fusion models
    """
    import torch
    
    # Load and preprocess images
    rgb_img = cv2.imread(rgb_path)
    thermal_img = cv2.imread(thermal_path)
    
    if rgb_img is None or thermal_img is None:
        return []
    
    # Resize images
    rgb_resized = cv2.resize(rgb_img, img_size)
    thermal_resized = cv2.resize(thermal_img, img_size)
    
    # Convert to RGB and normalize
    rgb_rgb = cv2.cvtColor(rgb_resized, cv2.COLOR_BGR2RGB)
    thermal_rgb = cv2.cvtColor(thermal_resized, cv2.COLOR_BGR2RGB)
    
    # Convert to tensors
    rgb_tensor = torch.from_numpy(rgb_rgb).permute(2, 0, 1).float() / 255.0
    thermal_tensor = torch.from_numpy(thermal_rgb).permute(2, 0, 1).float() / 255.0
    
    rgb_tensor = rgb_tensor.unsqueeze(0)
    thermal_tensor = thermal_tensor.unsqueeze(0)
    
    # Run inference
    with torch.no_grad():
        if hasattr(model, 'forward_dual'):
            pred = model.forward_dual(rgb_tensor, thermal_tensor)
        elif hasattr(model, '__call__'):
            pred = model([rgb_tensor, thermal_tensor])
        else:
            pred = model(rgb_tensor, thermal_tensor)
    
    return parse_fusion_results(pred, conf_threshold, rgb_img.shape[:2], img_size)

def run_early_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold):
    """
    Run inference for early fusion models (concatenated channels)
    """
    import torch
    
    # Load images
    rgb_img = cv2.imread(rgb_path)
    thermal_img = cv2.imread(thermal_path)
    
    if rgb_img is None or thermal_img is None:
        return []
    
    # Resize images
    rgb_resized = cv2.resize(rgb_img, img_size)
    thermal_resized = cv2.resize(thermal_img, img_size)
    
    # Convert thermal to grayscale if needed
    if len(thermal_resized.shape) == 3:
        thermal_gray = cv2.cvtColor(thermal_resized, cv2.COLOR_BGR2GRAY)
    else:
        thermal_gray = thermal_resized
    
    # Concatenate RGB + Thermal (4 channels)
    rgb_rgb = cv2.cvtColor(rgb_resized, cv2.COLOR_BGR2RGB)
    fusion_img = np.dstack([rgb_rgb, thermal_gray])
    
    # Convert to tensor
    fusion_tensor = torch.from_numpy(fusion_img).permute(2, 0, 1).float() / 255.0
    fusion_tensor = fusion_tensor.unsqueeze(0)
    
    # Run inference
    with torch.no_grad():
        pred = model(fusion_tensor)
    
    return parse_fusion_results(pred, conf_threshold, rgb_img.shape[:2], img_size)

def run_attention_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold):
    """
    Run inference for attention-based fusion models
    """
    # Similar to dual stream but may have different input format
    return run_dual_stream_inference(model, rgb_path, thermal_path, img_size, conf_threshold)

def run_custom_dual_inference(model, rgb_path, thermal_path, img_size, conf_threshold):
    """
    Run inference for custom dual-modal models
    """
    try:
        if hasattr(model, 'dual_inference'):
            results = model.dual_inference(rgb_path, thermal_path)
        elif hasattr(model, 'forward_dual'):
            # Load and preprocess images manually
            rgb_img = cv2.imread(rgb_path)
            thermal_img = cv2.imread(thermal_path)
            results = model.forward_dual(rgb_img, thermal_img)
        else:
            # Try passing paths as tuple
            results = model((rgb_path, thermal_path))
        
        return parse_fusion_results(results, conf_threshold, None, img_size)
        
    except Exception as e:
        print(f"Custom dual inference error: {e}")
        return []

def run_generic_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold):
    """
    Generic fusion inference - try multiple approaches
    """
    # Try method 1: Pass both paths
    try:
        results = model([rgb_path, thermal_path])
        return parse_fusion_results(results, conf_threshold, None, img_size)
    except:
        pass
    
    # Try method 2: Early fusion
    try:
        return run_early_fusion_inference(model, rgb_path, thermal_path, img_size, conf_threshold)
    except:
        pass
    
    # Try method 3: RGB only as fallback
    try:
        results = model(rgb_path)
        return parse_fusion_results(results, conf_threshold, None, img_size)
    except:
        pass
    
    return []

def parse_fusion_results(results, conf_threshold, original_shape=None, img_size=(640, 640)):
    """
    Parse fusion model results into standard format
    """
    try:
        detections = []
        
        if results is None:
            return detections
        
        # Handle different result formats
        if hasattr(results, 'pandas'):
            # YOLOv5-style results
            df = results.pandas().xyxy[0]
            if len(df) > 0:
                df = df[df['confidence'] >= conf_threshold]
                for _, row in df.iterrows():
                    detections.append({
                        'bbox': [row['xmin'], row['ymin'], row['xmax'], row['ymax']],
                        'confidence': row['confidence'],
                        'class': int(row['class'])
                    })
        
        elif hasattr(results, '__iter__') and len(results) > 0:
            # YOLOv8-style or list results
            result = results[0] if isinstance(results, list) else results
            if hasattr(result, 'boxes') and result.boxes is not None:
                boxes = result.boxes
                bboxes = boxes.xyxy.cpu().numpy()
                confidences = boxes.conf.cpu().numpy()
                classes = boxes.cls.cpu().numpy().astype(int)
                
                mask = confidences >= conf_threshold
                for i in range(len(bboxes)):
                    if mask[i]:
                        detections.append({
                            'bbox': bboxes[i].tolist(),
                            'confidence': float(confidences[i]),
                            'class': int(classes[i])
                        })
        
        elif hasattr(results, 'cpu') or isinstance(results, tuple):
            # Tensor results
            if isinstance(results, tuple):
                results = results[0]
            
            if hasattr(results, 'cpu'):
                results = results.cpu().numpy()
            
            if len(results.shape) == 3 and results.shape[0] == 1:
                results = results[0]
            
            if len(results) > 0 and results.shape[1] >= 6:
                confidences = results[:, 4]
                mask = confidences >= conf_threshold
                
                for i in range(len(results)):
                    if mask[i]:
                        bbox = results[i, :4]
                        conf = results[i, 4]
                        cls = int(results[i, 5])
                        
                        detections.append({
                            'bbox': bbox.tolist(),
                            'confidence': float(conf),
                            'class': cls
                        })
        
        return detections
        
    except Exception as e:
        print(f"Error parsing fusion results: {e}")
        return []

def batch_fusion_inference(model_path, rgb_dir, thermal_dir, output_file, img_size=(640, 640), conf_threshold=0.25):
    """
    Run batch fusion inference on RGB-Thermal image pairs
    """
    print(f"Loading fusion model from {model_path}")
    model, model_type = load_fusion_model(model_path)
    
    if model is None:
        print("Failed to load fusion model")
        return
    
    print(f"Detected model type: {model_type}")
    
    # Get RGB image files
    img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    rgb_files = []
    
    for ext in img_extensions:
        rgb_files.extend(Path(rgb_dir).glob(f'*{ext}'))
        rgb_files.extend(Path(rgb_dir).glob(f'*{ext.upper()}'))
    
    print(f"Found {len(rgb_files)} RGB images")
    
    # Run fusion inference
    all_detections = []
    processed = 0
    
    for rgb_file in rgb_files:
        if processed % 100 == 0:
            print(f"Processing {processed}/{len(rgb_files)}")
        
        img_id = rgb_file.stem
        thermal_file = Path(thermal_dir) / rgb_file.name
        
        if thermal_file.exists():
            detections = run_fusion_inference(model, model_type, str(rgb_file), str(thermal_file), 
                                            img_size, conf_threshold)
            
            for det in detections:
                all_detections.append({
                    'img_id': img_id,
                    'class': det['class'],
                    'confidence': det['confidence'],
                    'bbox': det['bbox']
                })
        else:
            print(f"Warning: Thermal image not found for {rgb_file.name}")
        
        processed += 1
    
    # Save results
    print(f"Saving {len(all_detections)} detections to {output_file}")
    
    with open(output_file, 'w') as f:
        for det in all_detections:
            bbox = det['bbox']
            f.write(f"{det['img_id']} {det['class']} {det['confidence']:.6f} "
                   f"{bbox[0]:.2f} {bbox[1]:.2f} {bbox[2]:.2f} {bbox[3]:.2f}\n")
    
    print("Batch fusion inference completed")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Fusion Model Inference Tool')
    parser.add_argument('--model_path', type=str, required=True, help='Path to fusion model file')
    parser.add_argument('--rgb_dir', type=str, required=True, help='Directory containing RGB images')
    parser.add_argument('--thermal_dir', type=str, required=True, help='Directory containing thermal images')
    parser.add_argument('--output_file', type=str, required=True, help='Output predictions file')
    parser.add_argument('--img_size', type=int, nargs=2, default=[640, 640], help='Image size for inference')
    parser.add_argument('--conf_threshold', type=float, default=0.25, help='Confidence threshold')
    
    args = parser.parse_args()
    
    batch_fusion_inference(args.model_path, args.rgb_dir, args.thermal_dir, args.output_file, 
                          tuple(args.img_size), args.conf_threshold)
