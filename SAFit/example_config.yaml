# RGBT-Tiny 多光谱数据集配置文件示例
# 支持RGB和热红外双模态训练和评估

# 数据集路径
path: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial

# RGB模态路径（绝对路径）
train_rgb: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/train/rgb
val_rgb: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb

# 热红外模态路径（绝对路径）
train_ir: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/train/thermal
val_ir: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/thermal

# 标签路径（RGB和thermal标签相同，使用RGB标签）
train_labels: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/train/rgb
val_labels: /media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb

# 类别数量
nc: 7

# 类别名称
names: ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']

# 图像尺寸 (width, height)
img_size: [640, 512]

# SAFit评估参数
safit_params:
  C: 32  # SAFit参数C，控制IoU和NWD的融合阈值
  iou_thresholds: [0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]  # SAFit阈值
  area_ranges:  # 目标尺度范围
    - [0, 1000000]     # all
    - [0, 64]          # extreme tiny (0-8²)
    - [64, 256]        # tiny (8²-16²)
    - [256, 1024]      # small (16²-32²)
    - [1024, 9216]     # medium (32²-96²)
    - [9216, 1000000]  # large (96²-∞)
  max_detections: [1, 10, 100]  # 最大检测数量
