"""
模型推理模块 - 支持YOLOv5/YOLOv8等模型的推理
适配双模态(RGB+Thermal)数据
"""

import torch
import cv2
import numpy as np
from pathlib import Path
import os
import yaml
from typing import List, Tuple, Dict, Union


class ModelInference:
    """模型推理类，支持多种YOLO模型"""
    
    def __init__(self, model_path: str, device: str = 'auto', conf_threshold: float = 0.001, 
                 iou_threshold: float = 0.6):
        """
        初始化模型推理器
        
        Args:
            model_path: 模型权重文件路径 (.pt文件)
            device: 推理设备 ('cpu', 'cuda', 'auto')
            conf_threshold: 置信度阈值
            iou_threshold: NMS的IoU阈值
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        
        # 设备选择
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 加载模型
        self.model = self._load_model()
        self.model.eval()
        
        print(f"模型加载成功: {model_path}")
        print(f"推理设备: {self.device}")
        print(f"置信度阈值: {conf_threshold}")
        print(f"NMS IoU阈值: {iou_threshold}")
    
    def _load_model(self):
        """加载模型"""
        try:
            # 尝试加载YOLOv5模型
            model = torch.load(self.model_path, map_location=self.device)
            if isinstance(model, dict) and 'model' in model:
                model = model['model']
            
            # 如果是YOLOv8或其他格式，尝试使用ultralytics
            if hasattr(model, 'float'):
                model = model.float()
            
            return model.to(self.device)
            
        except Exception as e:
            print(f"使用torch.load加载失败: {e}")
            try:
                # 尝试使用ultralytics加载
                from ultralytics import YOLO
                model = YOLO(self.model_path)
                return model
            except Exception as e2:
                print(f"使用ultralytics加载失败: {e2}")
                raise Exception(f"无法加载模型: {self.model_path}")
    
    def preprocess_image(self, image_path: str, img_size: int = 640) -> torch.Tensor:
        """
        预处理图像
        
        Args:
            image_path: 图像路径
            img_size: 目标图像尺寸
            
        Returns:
            预处理后的图像张量
        """
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 保存原始尺寸
        self.orig_shape = img.shape[:2]  # (height, width)
        
        # BGR转RGB
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # 调整尺寸并保持宽高比
        img_resized = self._letterbox(img, img_size)
        
        # 归一化并转换为张量
        img_tensor = img_resized.astype(np.float32) / 255.0
        img_tensor = torch.from_numpy(img_tensor).permute(2, 0, 1).unsqueeze(0)
        
        return img_tensor.to(self.device)
    
    def _letterbox(self, img: np.ndarray, new_shape: int = 640, color: Tuple[int, int, int] = (114, 114, 114)):
        """
        调整图像尺寸并保持宽高比
        
        Args:
            img: 输入图像
            new_shape: 目标尺寸
            color: 填充颜色
            
        Returns:
            调整后的图像
        """
        shape = img.shape[:2]  # current shape [height, width]
        
        # Scale ratio (new / old)
        r = min(new_shape / shape[0], new_shape / shape[1])
        
        # Compute padding
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape - new_unpad[0], new_shape - new_unpad[1]  # wh padding
        
        dw /= 2  # divide padding into 2 sides
        dh /= 2
        
        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
        
        # 保存缩放信息用于后处理
        self.scale_ratio = r
        self.pad = (dw, dh)
        
        return img
    
    def postprocess_detections(self, predictions, img_size: int = 640) -> Tuple[List, List, List]:
        """
        后处理检测结果
        
        Args:
            predictions: 模型预测结果
            img_size: 图像尺寸
            
        Returns:
            (boxes, scores, classes) - 检测框、分数、类别
        """
        # 处理不同模型的输出格式
        if hasattr(predictions, 'pred') and predictions.pred is not None:
            # YOLOv5格式
            pred = predictions.pred[0]
        elif isinstance(predictions, torch.Tensor):
            # 直接张量输出
            pred = predictions[0] if predictions.dim() == 3 else predictions
        elif hasattr(predictions, 'boxes'):
            # ultralytics格式
            return self._process_ultralytics_output(predictions)
        else:
            # 尝试直接处理
            pred = predictions
        
        if pred is None or len(pred) == 0:
            return [], [], []
        
        # 应用置信度阈值
        conf_mask = pred[:, 4] >= self.conf_threshold
        pred = pred[conf_mask]
        
        if len(pred) == 0:
            return [], [], []
        
        # 提取框、分数和类别
        boxes = pred[:, :4].cpu().numpy()
        scores = pred[:, 4].cpu().numpy()
        classes = pred[:, 5].cpu().numpy().astype(int)
        
        # 应用NMS
        keep_indices = self._apply_nms(boxes, scores, self.iou_threshold)
        
        boxes = boxes[keep_indices]
        scores = scores[keep_indices]
        classes = classes[keep_indices]
        
        # 将坐标转换回原始图像尺寸
        boxes = self._scale_coords(boxes, img_size, self.orig_shape)
        
        return boxes.tolist(), scores.tolist(), classes.tolist()
    
    def _process_ultralytics_output(self, predictions):
        """处理ultralytics模型输出"""
        boxes = []
        scores = []
        classes = []
        
        for result in predictions:
            if hasattr(result, 'boxes') and result.boxes is not None:
                box_data = result.boxes
                if len(box_data) > 0:
                    # 提取数据
                    boxes.extend(box_data.xyxy.cpu().numpy().tolist())
                    scores.extend(box_data.conf.cpu().numpy().tolist())
                    classes.extend(box_data.cls.cpu().numpy().astype(int).tolist())
        
        return boxes, scores, classes
    
    def _apply_nms(self, boxes: np.ndarray, scores: np.ndarray, iou_threshold: float) -> np.ndarray:
        """
        应用非极大值抑制
        
        Args:
            boxes: 检测框
            scores: 置信度分数
            iou_threshold: IoU阈值
            
        Returns:
            保留的索引
        """
        try:
            import torchvision
            boxes_tensor = torch.from_numpy(boxes)
            scores_tensor = torch.from_numpy(scores)
            keep = torchvision.ops.nms(boxes_tensor, scores_tensor, iou_threshold)
            return keep.numpy()
        except ImportError:
            # 如果没有torchvision，使用简单的NMS实现
            return self._simple_nms(boxes, scores, iou_threshold)
    
    def _simple_nms(self, boxes: np.ndarray, scores: np.ndarray, iou_threshold: float) -> np.ndarray:
        """简单的NMS实现"""
        indices = np.argsort(scores)[::-1]
        keep = []
        
        while len(indices) > 0:
            current = indices[0]
            keep.append(current)
            
            if len(indices) == 1:
                break
            
            # 计算IoU
            current_box = boxes[current]
            other_boxes = boxes[indices[1:]]
            
            ious = self._compute_iou(current_box, other_boxes)
            
            # 保留IoU小于阈值的框
            indices = indices[1:][ious < iou_threshold]
        
        return np.array(keep)
    
    def _compute_iou(self, box1: np.ndarray, boxes: np.ndarray) -> np.ndarray:
        """计算IoU"""
        x1 = np.maximum(box1[0], boxes[:, 0])
        y1 = np.maximum(box1[1], boxes[:, 1])
        x2 = np.minimum(box1[2], boxes[:, 2])
        y2 = np.minimum(box1[3], boxes[:, 3])
        
        intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)
        
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        
        union = area1 + area2 - intersection
        
        return intersection / (union + 1e-16)
    
    def _scale_coords(self, boxes: np.ndarray, img_size: int, orig_shape: Tuple[int, int]) -> np.ndarray:
        """将坐标从缩放后的图像转换回原始图像"""
        # 移除padding
        boxes[:, [0, 2]] -= self.pad[0]  # x padding
        boxes[:, [1, 3]] -= self.pad[1]  # y padding
        
        # 缩放回原始尺寸
        boxes /= self.scale_ratio
        
        # 裁剪到图像边界
        boxes[:, [0, 2]] = np.clip(boxes[:, [0, 2]], 0, orig_shape[1])  # width
        boxes[:, [1, 3]] = np.clip(boxes[:, [1, 3]], 0, orig_shape[0])  # height
        
        return boxes
    
    def predict(self, image_path: str, img_size: int = 640) -> Tuple[List, List, List]:
        """
        对单张图像进行预测
        
        Args:
            image_path: 图像路径
            img_size: 图像尺寸
            
        Returns:
            (boxes, scores, classes) - 检测框、分数、类别
        """
        # 预处理
        img_tensor = self.preprocess_image(image_path, img_size)
        
        # 推理
        with torch.no_grad():
            predictions = self.model(img_tensor)
        
        # 后处理
        boxes, scores, classes = self.postprocess_detections(predictions, img_size)
        
        return boxes, scores, classes
