"""
Model Inference Utilities for SAFit Evaluation
Supports various model formats and frameworks
"""

import os
import numpy as np
import cv2
from pathlib import Path

def load_model(model_path):
    """
    Load model from various formats
    """
    try:
        import torch
        
        # Try loading as PyTorch model
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        if model_path.endswith('.pt') or model_path.endswith('.pth'):
            # YOLOv5/YOLOv8 format
            try:
                model = torch.load(model_path, map_location=device)
                if isinstance(model, dict):
                    if 'model' in model:
                        model = model['model']
                    elif 'ema' in model:
                        model = model['ema']
                
                model.eval()
                return model, 'pytorch'
            except:
                # Try YOLOv5 hub loading
                try:
                    import yolov5
                    model = yolov5.load(model_path)
                    return model, 'yolov5'
                except:
                    pass
                
                # Try ultralytics YOLOv8
                try:
                    from ultralytics import YOLO
                    model = YOLO(model_path)
                    return model, 'yolov8'
                except:
                    pass
        
        print(f"Could not load model from {model_path}")
        return None, None
        
    except ImportError:
        print("PyTorch not available. Please install PyTorch to use model inference.")
        return None, None

def run_inference(model, model_type, img_path, img_size=(640, 640), conf_threshold=0.25):
    """
    Run inference on a single image
    """
    try:
        if model_type == 'pytorch':
            # Direct PyTorch model
            img = cv2.imread(img_path)
            if img is None:
                return []
            
            # Preprocess image
            img_resized = cv2.resize(img, img_size)
            img_rgb = cv2.cvtColor(img_resized, cv2.COLOR_BGR2RGB)
            img_tensor = torch.from_numpy(img_rgb).permute(2, 0, 1).float() / 255.0
            img_tensor = img_tensor.unsqueeze(0)
            
            # Run inference
            with torch.no_grad():
                pred = model(img_tensor)
            
            # Parse results (this is model-specific)
            detections = []
            if isinstance(pred, tuple):
                pred = pred[0]
            
            if pred is not None and len(pred) > 0:
                pred = pred[0]  # First image in batch
                pred = pred[pred[:, 4] > conf_threshold]  # Filter by confidence
                
                if len(pred) > 0:
                    # Scale back to original image size
                    scale_x = img.shape[1] / img_size[0]
                    scale_y = img.shape[0] / img_size[1]
                    
                    for det in pred:
                        x1, y1, x2, y2, conf = det[:5]
                        cls = int(det[5]) if len(det) > 5 else 0
                        
                        # Scale coordinates
                        x1 *= scale_x
                        y1 *= scale_y
                        x2 *= scale_x
                        y2 *= scale_y
                        
                        detections.append({
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'confidence': float(conf),
                            'class': cls
                        })
            
            return detections
            
        elif model_type == 'yolov5':
            # YOLOv5 hub model
            results = model(img_path, size=img_size[0])
            detections = []
            
            if hasattr(results, 'pandas'):
                df = results.pandas().xyxy[0]
                df = df[df['confidence'] >= conf_threshold]
                
                for _, row in df.iterrows():
                    detections.append({
                        'bbox': [row['xmin'], row['ymin'], row['xmax'], row['ymax']],
                        'confidence': row['confidence'],
                        'class': int(row['class'])
                    })
            
            return detections
            
        elif model_type == 'yolov8':
            # YOLOv8 ultralytics model
            results = model(img_path, imgsz=img_size[0], conf=conf_threshold)
            detections = []
            
            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes
                    for i in range(len(boxes)):
                        bbox = boxes.xyxy[i].cpu().numpy()
                        conf = boxes.conf[i].cpu().numpy()
                        cls = int(boxes.cls[i].cpu().numpy())
                        
                        detections.append({
                            'bbox': [float(bbox[0]), float(bbox[1]), float(bbox[2]), float(bbox[3])],
                            'confidence': float(conf),
                            'class': cls
                        })
            
            return detections
        
        else:
            print(f"Unsupported model type: {model_type}")
            return []
            
    except Exception as e:
        print(f"Error during inference on {img_path}: {e}")
        return []

def batch_inference(model_path, img_dir, output_file, img_size=(640, 640), conf_threshold=0.25):
    """
    Run batch inference on all images in a directory
    """
    print(f"Loading model from {model_path}")
    model, model_type = load_model(model_path)
    
    if model is None:
        print("Failed to load model")
        return
    
    print(f"Model type: {model_type}")
    
    # Get all image files
    img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    img_files = []
    
    for ext in img_extensions:
        img_files.extend(Path(img_dir).glob(f'*{ext}'))
        img_files.extend(Path(img_dir).glob(f'*{ext.upper()}'))
    
    print(f"Found {len(img_files)} images")
    
    # Run inference
    all_detections = []
    
    for i, img_file in enumerate(img_files):
        if i % 100 == 0:
            print(f"Processing {i}/{len(img_files)}")
        
        img_id = img_file.stem
        detections = run_inference(model, model_type, str(img_file), img_size, conf_threshold)
        
        for det in detections:
            all_detections.append({
                'img_id': img_id,
                'class': det['class'],
                'confidence': det['confidence'],
                'bbox': det['bbox']
            })
    
    # Save results
    print(f"Saving {len(all_detections)} detections to {output_file}")
    
    with open(output_file, 'w') as f:
        for det in all_detections:
            bbox = det['bbox']
            f.write(f"{det['img_id']} {det['class']} {det['confidence']:.6f} "
                   f"{bbox[0]:.2f} {bbox[1]:.2f} {bbox[2]:.2f} {bbox[3]:.2f}\n")
    
    print("Batch inference completed")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Model Inference Tool')
    parser.add_argument('--model_path', type=str, required=True, help='Path to model file')
    parser.add_argument('--img_dir', type=str, required=True, help='Directory containing images')
    parser.add_argument('--output_file', type=str, required=True, help='Output predictions file')
    parser.add_argument('--img_size', type=int, nargs=2, default=[640, 640], help='Image size for inference')
    parser.add_argument('--conf_threshold', type=float, default=0.25, help='Confidence threshold')
    
    args = parser.parse_args()
    
    batch_inference(args.model_path, args.img_dir, args.output_file, 
                   tuple(args.img_size), args.conf_threshold)
