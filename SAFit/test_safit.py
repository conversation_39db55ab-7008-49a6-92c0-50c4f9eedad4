#!/usr/bin/env python3
"""
Test script for SAFit evaluation tool
"""

import os
import sys
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_safit_utils():
    """Test SAFit utility functions"""
    print("Testing SAFit utility functions...")
    
    try:
        from safit_utils import sigmoid_np, iou_np, nwd_np, safit_np, yolo2xyxy_np
        
        # Test sigmoid function
        x = np.array([0, 16, 32, 64])
        k = 32
        result = sigmoid_np(x, k)
        print(f"Sigmoid test: {result}")
        
        # Test coordinate conversion
        yolo_bbox = np.array([[0.5, 0.5, 0.2, 0.3]])  # center_x, center_y, width, height
        xyxy_bbox = yolo2xyxy_np(yolo_bbox, 640, 512)
        print(f"YOLO to XYXY conversion: {yolo_bbox} -> {xyxy_bbox}")
        
        # Test IoU calculation
        bbox1 = np.array([[100, 100, 200, 200]])
        bbox2 = np.array([[150, 150, 250, 250]])
        iou_result = iou_np(bbox1, bbox2)
        print(f"IoU test: {iou_result}")
        
        # Test NWD calculation
        nwd_result = nwd_np(bbox1, bbox2, C=32)
        print(f"NWD test: {nwd_result}")
        
        # Test SAFit calculation
        safit_result = safit_np(bbox1, bbox2, C=32)
        print(f"SAFit test: {safit_result}")
        
        print("✓ SAFit utility functions test passed")
        return True
        
    except Exception as e:
        print(f"✗ SAFit utility functions test failed: {e}")
        return False

def test_evaluator():
    """Test SAFit evaluator"""
    print("\nTesting SAFit evaluator...")
    
    try:
        from safit_evaluator import SAFitEvaluator
        
        # Create evaluator
        evaluator = SAFitEvaluator()
        print(f"✓ Evaluator created with {evaluator.num_classes} classes")
        
        # Test with dummy data
        evaluator.gt_data = {
            'test_img': {
                'bboxes': np.array([[100, 100, 200, 200], [300, 300, 400, 400]]),
                'classes': np.array([0, 1]),
                'areas': np.array([10000, 10000])
            }
        }
        
        evaluator.pred_data = {
            'test_img': {
                'bboxes': np.array([[110, 110, 210, 210], [290, 290, 390, 390]]),
                'confidences': np.array([0.9, 0.8]),
                'classes': np.array([0, 1])
            }
        }
        
        # Test evaluation
        result = evaluator.evaluate_image('test_img', 0, [0, 1e5], 100)
        if result is not None:
            print("✓ Single image evaluation test passed")
        else:
            print("✗ Single image evaluation returned None")
            return False
        
        print("✓ SAFit evaluator test passed")
        return True
        
    except Exception as e:
        print(f"✗ SAFit evaluator test failed: {e}")
        return False

def test_file_operations():
    """Test file loading operations"""
    print("\nTesting file operations...")
    
    try:
        from safit_utils import load_yolo_labels
        
        # Create a temporary label file
        test_label_content = "0 0.5 0.5 0.2 0.3\n1 0.3 0.7 0.1 0.2\n"
        test_label_file = "test_label.txt"
        
        with open(test_label_file, 'w') as f:
            f.write(test_label_content)
        
        # Test loading
        labels = load_yolo_labels(test_label_file)
        print(f"Loaded labels shape: {labels.shape}")
        print(f"Labels content:\n{labels}")
        
        # Clean up
        os.remove(test_label_file)
        
        if labels.shape[0] == 2 and labels.shape[1] == 5:
            print("✓ File operations test passed")
            return True
        else:
            print("✗ File operations test failed: incorrect shape")
            return False
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("SAFit Evaluation Tool Test Suite")
    print("=" * 50)
    
    tests = [
        test_safit_utils,
        test_evaluator,
        test_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SAFit tool is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
