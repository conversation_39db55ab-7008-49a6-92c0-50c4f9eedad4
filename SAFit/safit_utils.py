# SAFit Evaluation Utils
# Modified from RGBT-Tiny codes/loss/utils.py and codes/metric/cocoeval.py

import numpy as np
import torch
import torch.nn.functional as F

def sigmoid_np(x, k):
    """NumPy version of sigmoid function"""
    return 1/(1+np.exp(-(1/k) * x))

def sigmoid_torch(x, k):
    """PyTorch version of sigmoid function"""
    return 1/(1+torch.exp(-(1/k) * x))

def yolo2xyxy_np(bbox, img_w, img_h):
    """Convert YOLO format (normalized cx,cy,w,h) to xyxy format (absolute x1,y1,x2,y2)"""
    _bbox = bbox.copy()
    _bbox[:,0] = (bbox[:,0] - bbox[:,2]/2) * img_w  # x1
    _bbox[:,1] = (bbox[:,1] - bbox[:,3]/2) * img_h  # y1
    _bbox[:,2] = (bbox[:,0] + bbox[:,2]/2) * img_w  # x2
    _bbox[:,3] = (bbox[:,1] + bbox[:,3]/2) * img_h  # y2
    return _bbox

def xyxy2xywh_np(bbox):
    """Convert xyxy format to xywh format for NWD calculation"""
    _bbox = bbox.copy()
    _bbox[:,0] = bbox[:,0]  # x
    _bbox[:,1] = bbox[:,1]  # y
    _bbox[:,2] = bbox[:,2] - bbox[:,0]  # w
    _bbox[:,3] = bbox[:,3] - bbox[:,1]  # h
    return _bbox

def iou_np(bb_test, bb_gt):
    """
    Calculate IoU between two sets of bboxes in xyxy format
    bb_test: (N, 4) predicted bboxes
    bb_gt: (M, 4) ground truth bboxes
    Returns: (N, M) IoU matrix
    """
    bb_gt = np.expand_dims(bb_gt, 0)  # (1, M, 4)
    bb_test = np.expand_dims(bb_test, 1)  # (N, 1, 4)

    xx1 = np.maximum(bb_test[..., 0], bb_gt[..., 0])
    yy1 = np.maximum(bb_test[..., 1], bb_gt[..., 1])
    xx2 = np.minimum(bb_test[..., 2], bb_gt[..., 2])
    yy2 = np.minimum(bb_test[..., 3], bb_gt[..., 3])
    
    w = np.maximum(0., xx2 - xx1)
    h = np.maximum(0., yy2 - yy1)
    wh = w * h
    
    area_test = (bb_test[..., 2] - bb_test[..., 0]) * (bb_test[..., 3] - bb_test[..., 1])
    area_gt = (bb_gt[..., 2] - bb_gt[..., 0]) * (bb_gt[..., 3] - bb_gt[..., 1])
    
    o = wh / (area_test + area_gt - wh + 1e-7)
    return o

def nwd_np(bb_test, bb_gt, C):
    """
    Calculate Normalized Wasserstein Distance
    bb_test: (N, 4) predicted bboxes in xyxy format
    bb_gt: (M, 4) ground truth bboxes in xyxy format
    C: normalization constant
    Returns: (N, M) NWD matrix
    """
    # Convert to xywh format and normalize w,h by 2
    bb_test_xywh = xyxy2xywh_np(bb_test)
    bb_gt_xywh = xyxy2xywh_np(bb_gt)
    
    bb_test_xywh[:,2:4] = bb_test_xywh[:,2:4]/2
    bb_gt_xywh[:,2:4] = bb_gt_xywh[:,2:4]/2
    
    bb_gt_xywh = np.expand_dims(bb_gt_xywh, 0)  # (1, M, 4)
    bb_test_xywh = np.expand_dims(bb_test_xywh, 1)  # (N, 1, 4)
    
    # Calculate Euclidean distance
    dist = np.sqrt(np.sum((bb_test_xywh - bb_gt_xywh)**2, axis=2))
    
    return np.exp(-dist/C)

def safit_np(bb_test, bb_gt, C=32):
    """
    Calculate SAFit metric
    bb_test: (N, 4) predicted bboxes in xyxy format
    bb_gt: (M, 4) ground truth bboxes in xyxy format
    C: normalization constant
    Returns: (N, M) SAFit matrix
    """
    if bb_test.shape[0] == 0 or bb_gt.shape[0] == 0:
        return np.array([])
    
    # Calculate IoU
    iou = iou_np(bb_test, bb_gt)
    
    # Calculate NWD
    nwd = nwd_np(bb_test, bb_gt, C)
    
    # Calculate area of ground truth bboxes
    bb_gt_expanded = np.expand_dims(bb_gt, 0)  # (1, M, 4)
    area_gt = (bb_gt_expanded[..., 2] - bb_gt_expanded[..., 0]) * (bb_gt_expanded[..., 3] - bb_gt_expanded[..., 1])
    area_gt = np.repeat(area_gt, bb_test.shape[0], axis=0)  # (N, M)
    area_gt = np.abs(area_gt)
    
    # Calculate SAFit using sigmoid weighting
    weight = sigmoid_np(np.sqrt(area_gt) - C, C)
    safit = weight * iou + (1 - weight) * nwd
    
    return safit

def load_yolo_labels(label_path):
    """
    Load YOLO format labels from file
    Returns: numpy array of shape (N, 5) where columns are [class, cx, cy, w, h]
    """
    try:
        with open(label_path, 'r') as f:
            lines = f.readlines()
        
        if not lines:
            return np.empty((0, 5))
        
        labels = []
        for line in lines:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 5:
                    labels.append([float(x) for x in parts[:5]])
        
        return np.array(labels) if labels else np.empty((0, 5))
    except:
        return np.empty((0, 5))

def parse_yolo_predictions(pred_file):
    """
    Parse YOLO prediction results
    Expected format: image_path x1 y1 x2 y2 confidence class_id
    or: class_id confidence cx cy w h (normalized)
    """
    predictions = []
    try:
        with open(pred_file, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 6:
                    # Try to parse different formats
                    try:
                        if len(parts) == 7:  # image_path x1 y1 x2 y2 conf class
                            img_path = parts[0]
                            x1, y1, x2, y2, conf, cls = map(float, parts[1:])
                            predictions.append({
                                'image_path': img_path,
                                'bbox': [x1, y1, x2, y2],
                                'confidence': conf,
                                'class': int(cls)
                            })
                        elif len(parts) == 6:  # class conf cx cy w h (normalized)
                            cls, conf, cx, cy, w, h = map(float, parts)
                            predictions.append({
                                'bbox': [cx, cy, w, h],  # normalized format
                                'confidence': conf,
                                'class': int(cls),
                                'normalized': True
                            })
                    except:
                        continue
    except:
        pass
    
    return predictions
