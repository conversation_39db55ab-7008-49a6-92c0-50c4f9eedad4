"""
SAFit评估器 - 基于RGBT-Tiny项目的SAFit指标评估工具
支持YOLO格式标签和预训练模型的直接评估

修改自: codes/metric/cocoeval.py
适配: YOLO格式标签，支持双模态评估
"""

import numpy as np
import datetime
import time
from collections import defaultdict
import copy
import os
import cv2
import torch
from pathlib import Path


class SAFitEvaluator:
    """SAFit评估器，专门用于小目标检测的评估"""
    
    def __init__(self, class_names, img_size=640):
        """
        初始化SAFit评估器
        
        Args:
            class_names: 类别名称列表
            img_size: 图像尺寸，用于YOLO标签转换
        """
        self.class_names = class_names
        self.num_classes = len(class_names)
        self.img_size = img_size
        
        # 评估参数设置
        self.params = self._init_params()
        self.eval_results = {}
        self.stats = []
        
    def _init_params(self):
        """初始化评估参数"""
        params = {
            'iouThrs': np.linspace(.5, 0.95, int(np.round((0.95 - .5) / .05)) + 1, endpoint=True),
            'recThrs': np.linspace(.0, 1.00, int(np.round((1.00 - .0) / .01)) + 1, endpoint=True),
            'maxDets': [1, 10, 100],
            'areaRng': [[0 ** 2, 1e5 ** 2], [0 ** 2, 8 ** 2], [8 ** 2, 16 ** 2], 
                       [16 ** 2, 32 ** 2], [32 ** 2, 96 ** 2], [96 ** 2, 1e5 ** 2]],
            'areaRngLbl': ['all', 'tiny', 'extreme tiny', 'small', 'medium', 'large'],
            'useCats': True
        }
        return params
    
    def yolo_to_xyxy(self, yolo_bbox, img_width, img_height):
        """
        将YOLO格式(归一化的x_center, y_center, width, height)转换为xyxy格式
        
        Args:
            yolo_bbox: [x_center, y_center, width, height] (归一化坐标)
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            [x1, y1, x2, y2] (绝对坐标)
        """
        x_center, y_center, width, height = yolo_bbox
        
        # 转换为绝对坐标
        x_center *= img_width
        y_center *= img_height
        width *= img_width
        height *= img_height
        
        # 转换为xyxy格式
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        
        return [x1, y1, x2, y2]
    
    def xyxy_to_xywh(self, xyxy_bbox):
        """
        将xyxy格式转换为xywh格式(COCO格式)
        
        Args:
            xyxy_bbox: [x1, y1, x2, y2]
            
        Returns:
            [x, y, width, height]
        """
        x1, y1, x2, y2 = xyxy_bbox
        return [x1, y1, x2 - x1, y2 - y1]
    
    def load_yolo_labels(self, label_path, img_width, img_height):
        """
        加载YOLO格式标签文件
        
        Args:
            label_path: 标签文件路径
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            标签列表，每个标签包含[class_id, x1, y1, x2, y2]
        """
        labels = []
        if os.path.exists(label_path):
            with open(label_path, 'r') as f:
                for line in f.readlines():
                    line = line.strip()
                    if line:
                        parts = line.split()
                        class_id = int(parts[0])
                        yolo_bbox = [float(x) for x in parts[1:5]]
                        xyxy_bbox = self.yolo_to_xyxy(yolo_bbox, img_width, img_height)
                        labels.append([class_id] + xyxy_bbox)
        return labels
    
    def xyhw2xxyy(self, bbox):
        """转换bbox格式从xywh到xyxy"""
        output = bbox.copy()
        output[:, 0] = bbox[:, 0]
        output[:, 1] = bbox[:, 1]
        output[:, 2] = bbox[:, 0] + bbox[:, 2]
        output[:, 3] = bbox[:, 1] + bbox[:, 3]
        return output
    
    def iou(self, bb_test, bb_gt):
        """
        计算IoU
        From SORT: Computes IOU between two bboxes in the form [x1,y1,x2,y2]
        """
        bb_test, bb_gt = self.xyhw2xxyy(bb_test), self.xyhw2xxyy(bb_gt)
        bb_gt = np.expand_dims(bb_gt, 0)
        bb_test = np.expand_dims(bb_test, 1)

        xx1 = np.maximum(bb_test[..., 0], bb_gt[..., 0])
        yy1 = np.maximum(bb_test[..., 1], bb_gt[..., 1])
        xx2 = np.minimum(bb_test[..., 2], bb_gt[..., 2])
        yy2 = np.minimum(bb_test[..., 3], bb_gt[..., 3])
        w = np.maximum(0., xx2 - xx1)
        h = np.maximum(0., yy2 - yy1)
        wh = w * h
        o = wh / ((bb_test[..., 2] - bb_test[..., 0]) * (bb_test[..., 3] - bb_test[..., 1])
                  + (bb_gt[..., 2] - bb_gt[..., 0]) * (bb_gt[..., 3] - bb_gt[..., 1]) - wh + 1e-7)
        return o
    
    def NWD(self, bb_test, bb_gt, C):
        """计算归一化Wasserstein距离"""
        bb_test[:, 2:4], bb_gt[:, 2:4] = bb_test[:, 2:4]/2, bb_gt[:, 2:4]/2
        bb_gt = np.expand_dims(bb_gt, 0)  # (1,b,4)
        bb_test = np.expand_dims(bb_test, 1)  # (b,1,4)
        return np.exp(-np.sqrt(np.sum((bb_test - bb_gt)*(bb_test - bb_gt), axis=2))/C)
    
    def sigmoid(self, x, k):
        """Sigmoid函数"""
        return 1/(1+np.exp(-(1/k) * x))
    
    def SAfit(self, bb_test, bb_gt, C=32):
        """
        计算SAFit指标
        
        Args:
            bb_test: 预测框 [x, y, w, h]
            bb_gt: 真实框 [x, y, w, h]
            C: SAFit参数，默认32
            
        Returns:
            SAFit分数矩阵
        """
        bb_test1, bb_gt1 = bb_test.copy(), bb_gt.copy()
        bb_test2, bb_gt2 = bb_test.copy(), bb_gt.copy()
        bb_gt = np.expand_dims(bb_gt, 0)
        Area_gt = ((bb_gt[..., 2])*(bb_gt[..., 3])).repeat(bb_test.shape[0], axis=0)
        Area_gt = np.abs(Area_gt)
        iou = self.iou(bb_test1, bb_gt1)
        nwd = self.NWD(bb_test2, bb_gt2, C)
        ious = (self.sigmoid(np.sqrt(Area_gt)-C, C)) * iou + \
               (1-self.sigmoid(np.sqrt(Area_gt)-C, C)) * nwd
        return ious

    def compute_safit_metrics(self, pred_boxes, pred_scores, pred_classes, gt_boxes, gt_classes, C=32):
        """
        计算SAFit评估指标

        Args:
            pred_boxes: 预测框列表 [[x1,y1,x2,y2], ...]
            pred_scores: 预测分数列表
            pred_classes: 预测类别列表
            gt_boxes: 真实框列表 [[x1,y1,x2,y2], ...]
            gt_classes: 真实类别列表
            C: SAFit参数

        Returns:
            评估结果字典
        """
        if len(pred_boxes) == 0 or len(gt_boxes) == 0:
            return {'safit_scores': np.array([]), 'matches': np.array([])}

        # 转换为numpy数组
        pred_boxes = np.array(pred_boxes)
        gt_boxes = np.array(gt_boxes)
        pred_scores = np.array(pred_scores)
        pred_classes = np.array(pred_classes)
        gt_classes = np.array(gt_classes)

        # 转换为xywh格式用于SAFit计算
        pred_xywh = np.array([self.xyxy_to_xywh(box) for box in pred_boxes])
        gt_xywh = np.array([self.xyxy_to_xywh(box) for box in gt_boxes])

        # 计算SAFit分数
        safit_scores = self.SAfit(pred_xywh, gt_xywh, C)

        # 按分数排序预测框
        sorted_indices = np.argsort(-pred_scores)

        results = {
            'safit_scores': safit_scores,
            'sorted_indices': sorted_indices,
            'pred_boxes': pred_boxes,
            'gt_boxes': gt_boxes,
            'pred_scores': pred_scores,
            'pred_classes': pred_classes,
            'gt_classes': gt_classes
        }

        return results

    def evaluate_image(self, pred_boxes, pred_scores, pred_classes, gt_boxes, gt_classes,
                      iou_thresholds=None, C=32):
        """
        评估单张图像

        Args:
            pred_boxes: 预测框 [[x1,y1,x2,y2], ...]
            pred_scores: 预测分数
            pred_classes: 预测类别
            gt_boxes: 真实框 [[x1,y1,x2,y2], ...]
            gt_classes: 真实类别
            iou_thresholds: IoU阈值列表
            C: SAFit参数

        Returns:
            评估结果
        """
        if iou_thresholds is None:
            iou_thresholds = self.params['iouThrs']

        # 计算SAFit指标
        metrics = self.compute_safit_metrics(pred_boxes, pred_scores, pred_classes,
                                           gt_boxes, gt_classes, C)

        if len(pred_boxes) == 0 or len(gt_boxes) == 0:
            return {
                'tp': np.zeros((len(iou_thresholds), len(pred_boxes))),
                'fp': np.zeros((len(iou_thresholds), len(pred_boxes))),
                'scores': pred_scores,
                'num_gt': len(gt_boxes)
            }

        safit_scores = metrics['safit_scores']
        sorted_indices = metrics['sorted_indices']

        # 初始化匹配结果
        num_pred = len(pred_boxes)
        num_gt = len(gt_boxes)
        num_thresholds = len(iou_thresholds)

        tp = np.zeros((num_thresholds, num_pred))
        fp = np.zeros((num_thresholds, num_pred))

        # 对每个IoU阈值进行评估
        for t_idx, threshold in enumerate(iou_thresholds):
            gt_matched = np.zeros(num_gt, dtype=bool)

            for p_idx in sorted_indices:
                # 找到最佳匹配的GT
                best_gt_idx = -1
                best_score = threshold

                for g_idx in range(num_gt):
                    if gt_matched[g_idx]:
                        continue

                    # 检查类别是否匹配
                    if pred_classes[p_idx] != gt_classes[g_idx]:
                        continue

                    # 检查SAFit分数
                    score = safit_scores[p_idx, g_idx]
                    if score > best_score:
                        best_score = score
                        best_gt_idx = g_idx

                # 记录匹配结果
                if best_gt_idx >= 0:
                    tp[t_idx, p_idx] = 1
                    gt_matched[best_gt_idx] = True
                else:
                    fp[t_idx, p_idx] = 1

        return {
            'tp': tp,
            'fp': fp,
            'scores': pred_scores[sorted_indices],
            'num_gt': num_gt,
            'safit_scores': safit_scores
        }

    def compute_ap(self, tp, fp, scores, num_gt):
        """
        计算Average Precision

        Args:
            tp: True Positive数组
            fp: False Positive数组
            scores: 预测分数
            num_gt: 真实目标数量

        Returns:
            AP值
        """
        if num_gt == 0:
            return 0.0

        # 累积计算
        tp_cumsum = np.cumsum(tp)
        fp_cumsum = np.cumsum(fp)

        # 计算recall和precision
        recall = tp_cumsum / num_gt
        precision = tp_cumsum / (tp_cumsum + fp_cumsum + 1e-16)

        # 计算AP (使用101点插值)
        recall_thresholds = self.params['recThrs']
        ap = 0.0

        for r in recall_thresholds:
            # 找到recall >= r的最大precision
            valid_indices = recall >= r
            if np.any(valid_indices):
                ap += np.max(precision[valid_indices])

        ap /= len(recall_thresholds)
        return ap
