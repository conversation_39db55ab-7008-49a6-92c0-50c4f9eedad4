# SAFit Evaluator
# Modified from RGBT-Tiny codes/metric/cocoeval.py

import numpy as np
import os
import time
from collections import defaultdict
import copy
from .safit_utils import *

class SAFitEvaluator:
    """
    SAFit Evaluator for object detection evaluation using SAFit metric
    Supports both single-modal and dual-modal evaluation
    """
    
    def __init__(self, class_names=None, iou_thresholds=None, area_ranges=None):
        """
        Initialize SAFit Evaluator
        
        Args:
            class_names: list of class names
            iou_thresholds: list of IoU thresholds for evaluation
            area_ranges: list of area ranges for evaluation
        """
        self.class_names = class_names or ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']
        self.num_classes = len(self.class_names)
        
        # Default IoU thresholds (using SAFit instead of IoU)
        self.iou_thresholds = iou_thresholds or np.linspace(0.5, 0.95, 10)
        
        # Area ranges for different object sizes
        self.area_ranges = area_ranges or [
            [0**2, 1e5**2],      # all
            [0**2, 8**2],        # extreme tiny  
            [8**2, 16**2],       # tiny
            [16**2, 32**2],      # small
            [32**2, 96**2],      # medium
            [96**2, 1e5**2]      # large
        ]
        self.area_range_labels = ['all', 'extreme_tiny', 'tiny', 'small', 'medium', 'large']
        
        # Recall thresholds
        self.recall_thresholds = np.linspace(0.0, 1.0, 101)
        
        # Max detections
        self.max_detections = [1, 10, 100]
        
        # SAFit parameter
        self.C = 32
        
        # Results storage
        self.eval_imgs = []
        self.eval_results = {}
        self.stats = []
        
    def load_ground_truth(self, gt_dir, img_size=(640, 512)):
        """
        Load ground truth labels from YOLO format files
        
        Args:
            gt_dir: directory containing YOLO format label files
            img_size: (width, height) of images
        """
        self.gt_data = {}
        self.img_size = img_size
        
        if not os.path.exists(gt_dir):
            print(f"Ground truth directory not found: {gt_dir}")
            return
        
        label_files = [f for f in os.listdir(gt_dir) if f.endswith('.txt')]
        
        for label_file in label_files:
            img_id = os.path.splitext(label_file)[0]
            label_path = os.path.join(gt_dir, label_file)
            
            # Load YOLO format labels
            labels = load_yolo_labels(label_path)
            
            if labels.shape[0] > 0:
                # Convert from normalized YOLO format to absolute xyxy
                bboxes = labels[:, 1:5]  # cx, cy, w, h (normalized)
                classes = labels[:, 0].astype(int)
                
                # Convert to absolute coordinates
                bboxes_abs = yolo2xyxy_np(bboxes, self.img_size[0], self.img_size[1])
                
                self.gt_data[img_id] = {
                    'bboxes': bboxes_abs,
                    'classes': classes,
                    'areas': (bboxes_abs[:, 2] - bboxes_abs[:, 0]) * (bboxes_abs[:, 3] - bboxes_abs[:, 1])
                }
            else:
                self.gt_data[img_id] = {
                    'bboxes': np.empty((0, 4)),
                    'classes': np.empty((0,), dtype=int),
                    'areas': np.empty((0,))
                }
    
    def load_predictions_from_model(self, model_path, img_dir, conf_threshold=0.001):
        """
        Load predictions from a trained model (best.pt)
        
        Args:
            model_path: path to the trained model file
            img_dir: directory containing test images
            conf_threshold: confidence threshold for filtering predictions
        """
        try:
            import torch
            
            # Load model
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = torch.load(model_path, map_location=device)
            
            if isinstance(model, dict) and 'model' in model:
                model = model['model']
            
            model.eval()
            
            self.pred_data = {}
            
            # Get image files
            img_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
            img_files = []
            for ext in img_extensions:
                img_files.extend([f for f in os.listdir(img_dir) if f.lower().endswith(ext)])
            
            print(f"Processing {len(img_files)} images...")
            
            for img_file in img_files:
                img_id = os.path.splitext(img_file)[0]
                img_path = os.path.join(img_dir, img_file)
                
                # Run inference
                try:
                    results = model(img_path)
                    
                    # Parse results (this depends on the model format)
                    if hasattr(results, 'pandas'):
                        # YOLOv5 format
                        df = results.pandas().xyxy[0]
                        if len(df) > 0:
                            bboxes = df[['xmin', 'ymin', 'xmax', 'ymax']].values
                            confidences = df['confidence'].values
                            classes = df['class'].values.astype(int)
                            
                            # Filter by confidence
                            mask = confidences >= conf_threshold
                            bboxes = bboxes[mask]
                            confidences = confidences[mask]
                            classes = classes[mask]
                        else:
                            bboxes = np.empty((0, 4))
                            confidences = np.empty((0,))
                            classes = np.empty((0,), dtype=int)
                    else:
                        # Try to parse other formats
                        bboxes = np.empty((0, 4))
                        confidences = np.empty((0,))
                        classes = np.empty((0,), dtype=int)
                    
                    self.pred_data[img_id] = {
                        'bboxes': bboxes,
                        'confidences': confidences,
                        'classes': classes
                    }
                    
                except Exception as e:
                    print(f"Error processing {img_file}: {e}")
                    self.pred_data[img_id] = {
                        'bboxes': np.empty((0, 4)),
                        'confidences': np.empty((0,)),
                        'classes': np.empty((0,), dtype=int)
                    }
            
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Please make sure PyTorch and the model are compatible")
            self.pred_data = {}
    
    def load_predictions_from_file(self, pred_file):
        """
        Load predictions from a text file
        Expected format: img_id class_id confidence x1 y1 x2 y2
        """
        self.pred_data = {}
        
        try:
            with open(pred_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 7:
                        img_id = parts[0]
                        class_id = int(parts[1])
                        confidence = float(parts[2])
                        x1, y1, x2, y2 = map(float, parts[3:7])
                        
                        if img_id not in self.pred_data:
                            self.pred_data[img_id] = {
                                'bboxes': [],
                                'confidences': [],
                                'classes': []
                            }
                        
                        self.pred_data[img_id]['bboxes'].append([x1, y1, x2, y2])
                        self.pred_data[img_id]['confidences'].append(confidence)
                        self.pred_data[img_id]['classes'].append(class_id)
            
            # Convert lists to numpy arrays
            for img_id in self.pred_data:
                self.pred_data[img_id]['bboxes'] = np.array(self.pred_data[img_id]['bboxes'])
                self.pred_data[img_id]['confidences'] = np.array(self.pred_data[img_id]['confidences'])
                self.pred_data[img_id]['classes'] = np.array(self.pred_data[img_id]['classes'])
                
        except Exception as e:
            print(f"Error loading predictions from file: {e}")
            self.pred_data = {}
    
    def evaluate_image(self, img_id, class_id, area_range, max_det):
        """
        Evaluate single image for specific class and area range
        """
        if img_id not in self.gt_data or img_id not in self.pred_data:
            return None
        
        gt = self.gt_data[img_id]
        pred = self.pred_data[img_id]
        
        # Filter by class
        gt_mask = gt['classes'] == class_id
        pred_mask = pred['classes'] == class_id
        
        gt_bboxes = gt['bboxes'][gt_mask]
        gt_areas = gt['areas'][gt_mask]
        pred_bboxes = pred['bboxes'][pred_mask]
        pred_confidences = pred['confidences'][pred_mask]
        
        if len(gt_bboxes) == 0 and len(pred_bboxes) == 0:
            return None
        
        # Filter GT by area range
        area_mask = (gt_areas >= area_range[0]) & (gt_areas <= area_range[1])
        gt_bboxes = gt_bboxes[area_mask]
        gt_areas = gt_areas[area_mask]
        
        # Sort predictions by confidence
        if len(pred_bboxes) > 0:
            sort_idx = np.argsort(-pred_confidences)
            pred_bboxes = pred_bboxes[sort_idx]
            pred_confidences = pred_confidences[sort_idx]
            
            # Limit to max detections
            if len(pred_bboxes) > max_det:
                pred_bboxes = pred_bboxes[:max_det]
                pred_confidences = pred_confidences[:max_det]
        
        # Calculate SAFit scores
        if len(gt_bboxes) > 0 and len(pred_bboxes) > 0:
            safit_scores = safit_np(pred_bboxes, gt_bboxes, self.C)
        else:
            safit_scores = np.array([])
        
        return {
            'img_id': img_id,
            'class_id': class_id,
            'area_range': area_range,
            'max_det': max_det,
            'gt_bboxes': gt_bboxes,
            'pred_bboxes': pred_bboxes,
            'pred_confidences': pred_confidences,
            'safit_scores': safit_scores
        }
