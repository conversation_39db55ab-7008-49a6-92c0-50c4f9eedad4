# SAFit Evaluator
# Modified from RGBT-Tiny codes/metric/cocoeval.py

import numpy as np
import os
import time
from collections import defaultdict
import copy
try:
    from .safit_utils import *
except ImportError:
    from safit_utils import *

class SAFitEvaluator:
    """
    SAFit Evaluator for object detection evaluation using SAFit metric
    Supports both single-modal and dual-modal evaluation
    """
    
    def __init__(self, class_names=None, iou_thresholds=None, area_ranges=None):
        """
        Initialize SAFit Evaluator
        
        Args:
            class_names: list of class names
            iou_thresholds: list of IoU thresholds for evaluation
            area_ranges: list of area ranges for evaluation
        """
        self.class_names = class_names or ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']
        self.num_classes = len(self.class_names)
        
        # Default IoU thresholds (using SAFit instead of IoU)
        self.iou_thresholds = iou_thresholds or np.linspace(0.5, 0.95, 10)
        
        # Area ranges for different object sizes
        self.area_ranges = area_ranges or [
            [0**2, 1e5**2],      # all
            [0**2, 8**2],        # extreme tiny  
            [8**2, 16**2],       # tiny
            [16**2, 32**2],      # small
            [32**2, 96**2],      # medium
            [96**2, 1e5**2]      # large
        ]
        self.area_range_labels = ['all', 'extreme_tiny', 'tiny', 'small', 'medium', 'large']
        
        # Recall thresholds
        self.recall_thresholds = np.linspace(0.0, 1.0, 101)
        
        # Max detections
        self.max_detections = [1, 10, 100]
        
        # SAFit parameter
        self.C = 32
        
        # Results storage
        self.eval_imgs = []
        self.eval_results = {}
        self.stats = []
        
    def load_ground_truth(self, gt_dir, img_size=(640, 512)):
        """
        Load ground truth labels from YOLO format files
        
        Args:
            gt_dir: directory containing YOLO format label files
            img_size: (width, height) of images
        """
        self.gt_data = {}
        self.img_size = img_size
        
        if not os.path.exists(gt_dir):
            print(f"Ground truth directory not found: {gt_dir}")
            return
        
        label_files = [f for f in os.listdir(gt_dir) if f.endswith('.txt')]
        
        for label_file in label_files:
            img_id = os.path.splitext(label_file)[0]
            label_path = os.path.join(gt_dir, label_file)
            
            # Load YOLO format labels
            labels = load_yolo_labels(label_path)
            
            if labels.shape[0] > 0:
                # Convert from normalized YOLO format to absolute xyxy
                bboxes = labels[:, 1:5]  # cx, cy, w, h (normalized)
                classes = labels[:, 0].astype(int)
                
                # Convert to absolute coordinates
                bboxes_abs = yolo2xyxy_np(bboxes, self.img_size[0], self.img_size[1])
                
                self.gt_data[img_id] = {
                    'bboxes': bboxes_abs,
                    'classes': classes,
                    'areas': (bboxes_abs[:, 2] - bboxes_abs[:, 0]) * (bboxes_abs[:, 3] - bboxes_abs[:, 1])
                }
            else:
                self.gt_data[img_id] = {
                    'bboxes': np.empty((0, 4)),
                    'classes': np.empty((0,), dtype=int),
                    'areas': np.empty((0,))
                }
    
    def load_predictions_from_model(self, model_path, img_dir, conf_threshold=0.001):
        """
        Load predictions from a trained model (best.pt)
        
        Args:
            model_path: path to the trained model file
            img_dir: directory containing test images
            conf_threshold: confidence threshold for filtering predictions
        """
        try:
            import torch
            
            # Load model
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = torch.load(model_path, map_location=device)
            
            if isinstance(model, dict) and 'model' in model:
                model = model['model']
            
            model.eval()
            
            self.pred_data = {}
            
            # Get image files
            img_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
            img_files = []
            for ext in img_extensions:
                img_files.extend([f for f in os.listdir(img_dir) if f.lower().endswith(ext)])
            
            print(f"Processing {len(img_files)} images...")
            
            for img_file in img_files:
                img_id = os.path.splitext(img_file)[0]
                img_path = os.path.join(img_dir, img_file)
                
                # Run inference
                try:
                    results = model(img_path)
                    
                    # Parse results (this depends on the model format)
                    if hasattr(results, 'pandas'):
                        # YOLOv5 format
                        df = results.pandas().xyxy[0]
                        if len(df) > 0:
                            bboxes = df[['xmin', 'ymin', 'xmax', 'ymax']].values
                            confidences = df['confidence'].values
                            classes = df['class'].values.astype(int)
                            
                            # Filter by confidence
                            mask = confidences >= conf_threshold
                            bboxes = bboxes[mask]
                            confidences = confidences[mask]
                            classes = classes[mask]
                        else:
                            bboxes = np.empty((0, 4))
                            confidences = np.empty((0,))
                            classes = np.empty((0,), dtype=int)
                    else:
                        # Try to parse other formats
                        bboxes = np.empty((0, 4))
                        confidences = np.empty((0,))
                        classes = np.empty((0,), dtype=int)
                    
                    self.pred_data[img_id] = {
                        'bboxes': bboxes,
                        'confidences': confidences,
                        'classes': classes
                    }
                    
                except Exception as e:
                    print(f"Error processing {img_file}: {e}")
                    self.pred_data[img_id] = {
                        'bboxes': np.empty((0, 4)),
                        'confidences': np.empty((0,)),
                        'classes': np.empty((0,), dtype=int)
                    }
            
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Please make sure PyTorch and the model are compatible")
            self.pred_data = {}
    
    def load_predictions_from_file(self, pred_file):
        """
        Load predictions from a text file
        Expected format: img_id class_id confidence x1 y1 x2 y2
        """
        self.pred_data = {}
        
        try:
            with open(pred_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 7:
                        img_id = parts[0]
                        class_id = int(parts[1])
                        confidence = float(parts[2])
                        x1, y1, x2, y2 = map(float, parts[3:7])
                        
                        if img_id not in self.pred_data:
                            self.pred_data[img_id] = {
                                'bboxes': [],
                                'confidences': [],
                                'classes': []
                            }
                        
                        self.pred_data[img_id]['bboxes'].append([x1, y1, x2, y2])
                        self.pred_data[img_id]['confidences'].append(confidence)
                        self.pred_data[img_id]['classes'].append(class_id)
            
            # Convert lists to numpy arrays
            for img_id in self.pred_data:
                self.pred_data[img_id]['bboxes'] = np.array(self.pred_data[img_id]['bboxes'])
                self.pred_data[img_id]['confidences'] = np.array(self.pred_data[img_id]['confidences'])
                self.pred_data[img_id]['classes'] = np.array(self.pred_data[img_id]['classes'])
                
        except Exception as e:
            print(f"Error loading predictions from file: {e}")
            self.pred_data = {}
    
    def evaluate_image(self, img_id, class_id, area_range, max_det):
        """
        Evaluate single image for specific class and area range
        """
        if img_id not in self.gt_data or img_id not in self.pred_data:
            return None
        
        gt = self.gt_data[img_id]
        pred = self.pred_data[img_id]
        
        # Filter by class
        gt_mask = gt['classes'] == class_id
        pred_mask = pred['classes'] == class_id
        
        gt_bboxes = gt['bboxes'][gt_mask]
        gt_areas = gt['areas'][gt_mask]
        pred_bboxes = pred['bboxes'][pred_mask]
        pred_confidences = pred['confidences'][pred_mask]
        
        if len(gt_bboxes) == 0 and len(pred_bboxes) == 0:
            return None
        
        # Filter GT by area range
        area_mask = (gt_areas >= area_range[0]) & (gt_areas <= area_range[1])
        gt_bboxes = gt_bboxes[area_mask]
        gt_areas = gt_areas[area_mask]
        
        # Sort predictions by confidence
        if len(pred_bboxes) > 0:
            sort_idx = np.argsort(-pred_confidences)
            pred_bboxes = pred_bboxes[sort_idx]
            pred_confidences = pred_confidences[sort_idx]
            
            # Limit to max detections
            if len(pred_bboxes) > max_det:
                pred_bboxes = pred_bboxes[:max_det]
                pred_confidences = pred_confidences[:max_det]
        
        # Calculate SAFit scores
        if len(gt_bboxes) > 0 and len(pred_bboxes) > 0:
            safit_scores = safit_np(pred_bboxes, gt_bboxes, self.C)
        else:
            safit_scores = np.array([])
        
        return {
            'img_id': img_id,
            'class_id': class_id,
            'area_range': area_range,
            'max_det': max_det,
            'gt_bboxes': gt_bboxes,
            'pred_bboxes': pred_bboxes,
            'pred_confidences': pred_confidences,
            'safit_scores': safit_scores
        }

    def evaluate(self):
        """
        Run evaluation on all images and classes
        """
        print("Running SAFit evaluation...")
        tic = time.time()

        self.eval_imgs = []

        # Get all image IDs
        img_ids = list(set(list(self.gt_data.keys()) + list(self.pred_data.keys())))

        # Evaluate for each combination
        for class_id in range(self.num_classes):
            for area_range in self.area_ranges:
                for max_det in self.max_detections:
                    for img_id in img_ids:
                        result = self.evaluate_image(img_id, class_id, area_range, max_det)
                        if result is not None:
                            self.eval_imgs.append(result)

        toc = time.time()
        print(f'Evaluation completed in {toc-tic:.2f}s')

    def accumulate(self):
        """
        Accumulate evaluation results and compute precision/recall
        """
        print("Accumulating evaluation results...")
        tic = time.time()

        if not self.eval_imgs:
            print("Please run evaluate() first")
            return

        T = len(self.iou_thresholds)  # Number of SAFit thresholds
        R = len(self.recall_thresholds)  # Number of recall thresholds
        K = self.num_classes  # Number of classes
        A = len(self.area_ranges)  # Number of area ranges
        M = len(self.max_detections)  # Number of max detection settings

        # Initialize arrays
        precision = -np.ones((T, R, K, A, M))
        recall = -np.ones((T, K, A, M))
        scores = -np.ones((T, R, K, A, M))

        # Group evaluation results
        eval_dict = defaultdict(list)
        for eval_img in self.eval_imgs:
            key = (eval_img['class_id'],
                   tuple(eval_img['area_range']),
                   eval_img['max_det'])
            eval_dict[key].append(eval_img)

        # Process each group
        for k in range(K):  # For each class
            for a, area_range in enumerate(self.area_ranges):  # For each area range
                for m, max_det in enumerate(self.max_detections):  # For each max detection

                    key = (k, tuple(area_range), max_det)
                    if key not in eval_dict:
                        continue

                    E = eval_dict[key]

                    # Collect all detections and ground truths
                    all_pred_confidences = []
                    all_safit_scores = []
                    all_gt_count = 0

                    for e in E:
                        if len(e['pred_confidences']) > 0:
                            all_pred_confidences.extend(e['pred_confidences'])
                            if len(e['safit_scores']) > 0:
                                # Get max SAFit score for each detection
                                max_safit = np.max(e['safit_scores'], axis=1)
                                all_safit_scores.extend(max_safit)
                            else:
                                all_safit_scores.extend([0.0] * len(e['pred_confidences']))

                        all_gt_count += len(e['gt_bboxes'])

                    if all_gt_count == 0:
                        continue

                    # Convert to numpy arrays
                    all_pred_confidences = np.array(all_pred_confidences)
                    all_safit_scores = np.array(all_safit_scores)

                    if len(all_pred_confidences) == 0:
                        continue

                    # Sort by confidence
                    sort_idx = np.argsort(-all_pred_confidences)
                    all_pred_confidences = all_pred_confidences[sort_idx]
                    all_safit_scores = all_safit_scores[sort_idx]

                    # Calculate precision and recall for each SAFit threshold
                    for t, safit_thr in enumerate(self.iou_thresholds):
                        # Determine matches based on SAFit threshold
                        matches = all_safit_scores >= safit_thr

                        # Calculate cumulative TP and FP
                        tp = np.cumsum(matches).astype(float)
                        fp = np.cumsum(~matches).astype(float)

                        # Calculate precision and recall
                        if len(tp) > 0:
                            recall_vals = tp / all_gt_count
                            precision_vals = tp / (tp + fp + np.finfo(float).eps)

                            # Store max recall
                            recall[t, k, a, m] = recall_vals[-1] if len(recall_vals) > 0 else 0

                            # Interpolate precision
                            precision_interp = np.zeros(R)
                            score_interp = np.zeros(R)

                            # Smooth precision curve
                            for i in range(len(precision_vals) - 1, 0, -1):
                                if precision_vals[i] > precision_vals[i-1]:
                                    precision_vals[i-1] = precision_vals[i]

                            # Interpolate at recall thresholds
                            inds = np.searchsorted(recall_vals, self.recall_thresholds, side='left')
                            for ri, pi in enumerate(inds):
                                if pi < len(precision_vals):
                                    precision_interp[ri] = precision_vals[pi]
                                    score_interp[ri] = all_pred_confidences[pi]

                            precision[t, :, k, a, m] = precision_interp
                            scores[t, :, k, a, m] = score_interp

        self.eval_results = {
            'precision': precision,
            'recall': recall,
            'scores': scores,
            'params': {
                'iou_thresholds': self.iou_thresholds,
                'recall_thresholds': self.recall_thresholds,
                'class_names': self.class_names,
                'area_ranges': self.area_ranges,
                'area_range_labels': self.area_range_labels,
                'max_detections': self.max_detections
            }
        }

        toc = time.time()
        print(f'Accumulation completed in {toc-tic:.2f}s')

    def summarize(self):
        """
        Compute and display summary metrics
        """
        if not self.eval_results:
            print("Please run accumulate() first")
            return

        def _summarize(ap=1, safit_thr=None, area_rng='all', max_dets=100):
            """
            Compute summary metric
            """
            p = self.eval_results['params']

            # Format strings
            iStr = ' {:<18} {} @[ SAFit={:<9} | area={:>6s} | maxDets={:>3d} ] = {:0.3f}'
            titleStr = 'Average Precision' if ap == 1 else 'Average Recall'
            typeStr = '(AP)' if ap == 1 else '(AR)'

            if safit_thr is None:
                safit_str = '{:0.2f}:{:0.2f}'.format(self.iou_thresholds[0], self.iou_thresholds[-1])
            else:
                safit_str = '{:0.2f}'.format(safit_thr)

            # Find area range index
            aind = [i for i, aRng in enumerate(p['area_range_labels']) if aRng == area_rng]
            if not aind:
                aind = [0]  # Default to 'all'
            aind = aind[0]

            # Find max detection index
            mind = [i for i, mDet in enumerate(p['max_detections']) if mDet == max_dets]
            if not mind:
                mind = [-1]  # Default to last
            mind = mind[0]

            if ap == 1:
                # Precision
                s = self.eval_results['precision']
                if safit_thr is not None:
                    t = np.where(np.abs(self.iou_thresholds - safit_thr) < 1e-6)[0]
                    if len(t) > 0:
                        s = s[t[0]]
                    else:
                        s = s[0]  # Default to first threshold
                else:
                    s = s  # All thresholds

                # Select area range and max detections
                if len(s.shape) == 5:  # All thresholds
                    s = s[:, :, :, aind, mind]
                else:  # Single threshold
                    s = s[:, :, aind, mind]
            else:
                # Recall
                s = self.eval_results['recall']
                if safit_thr is not None:
                    t = np.where(np.abs(self.iou_thresholds - safit_thr) < 1e-6)[0]
                    if len(t) > 0:
                        s = s[t[0]]
                    else:
                        s = s[0]
                else:
                    s = s

                # Select area range and max detections
                if len(s.shape) == 4:  # All thresholds
                    s = s[:, :, aind, mind]
                else:  # Single threshold
                    s = s[:, aind, mind]

            # Calculate mean
            if len(s[s > -1]) == 0:
                mean_s = -1
            else:
                mean_s = np.mean(s[s > -1])

            print(iStr.format(titleStr, typeStr, safit_str, area_rng, max_dets, mean_s))
            return mean_s

        # Calculate standard metrics
        stats = np.zeros((16,))

        # AP metrics
        stats[0] = _summarize(1)  # AP@[0.5:0.95]
        stats[1] = _summarize(1, safit_thr=0.5, max_dets=self.max_detections[2])  # AP@0.5
        stats[2] = _summarize(1, safit_thr=0.75, max_dets=self.max_detections[2])  # AP@0.75
        stats[3] = _summarize(1, area_rng='extreme_tiny', max_dets=self.max_detections[2])  # AP extreme tiny
        stats[4] = _summarize(1, area_rng='tiny', max_dets=self.max_detections[2])  # AP tiny
        stats[5] = _summarize(1, area_rng='small', max_dets=self.max_detections[2])  # AP small
        stats[6] = _summarize(1, area_rng='medium', max_dets=self.max_detections[2])  # AP medium
        stats[7] = _summarize(1, area_rng='large', max_dets=self.max_detections[2])  # AP large

        # AR metrics
        stats[8] = _summarize(0, max_dets=self.max_detections[0])  # AR@1
        stats[9] = _summarize(0, max_dets=self.max_detections[1])  # AR@10
        stats[10] = _summarize(0, max_dets=self.max_detections[2])  # AR@100
        stats[11] = _summarize(0, area_rng='extreme_tiny', max_dets=self.max_detections[2])  # AR extreme tiny
        stats[12] = _summarize(0, area_rng='tiny', max_dets=self.max_detections[2])  # AR tiny
        stats[13] = _summarize(0, area_rng='small', max_dets=self.max_detections[2])  # AR small
        stats[14] = _summarize(0, area_rng='medium', max_dets=self.max_detections[2])  # AR medium
        stats[15] = _summarize(0, area_rng='large', max_dets=self.max_detections[2])  # AR large

        self.stats = stats
        return stats

    def get_results_dict(self):
        """
        Get results as a dictionary
        """
        if not self.stats:
            print("Please run summarize() first")
            return {}

        results = {
            'AP': self.stats[0],
            'AP50': self.stats[1],
            'AP75': self.stats[2],
            'AP_extreme_tiny': self.stats[3],
            'AP_tiny': self.stats[4],
            'AP_small': self.stats[5],
            'AP_medium': self.stats[6],
            'AP_large': self.stats[7],
            'AR1': self.stats[8],
            'AR10': self.stats[9],
            'AR100': self.stats[10],
            'AR_extreme_tiny': self.stats[11],
            'AR_tiny': self.stats[12],
            'AR_small': self.stats[13],
            'AR_medium': self.stats[14],
            'AR_large': self.stats[15]
        }

        return results

    def save_results(self, output_file):
        """
        Save evaluation results to file
        """
        results = self.get_results_dict()

        with open(output_file, 'w') as f:
            f.write("SAFit Evaluation Results\n")
            f.write("=" * 50 + "\n")
            f.write(f"Dataset: {len(self.gt_data)} images\n")
            f.write(f"Classes: {self.class_names}\n")
            f.write(f"SAFit parameter C: {self.C}\n\n")

            f.write("Average Precision (AP) Metrics:\n")
            f.write(f"AP (0.5:0.95): {results['AP']:.3f}\n")
            f.write(f"AP50: {results['AP50']:.3f}\n")
            f.write(f"AP75: {results['AP75']:.3f}\n")
            f.write(f"AP extreme tiny: {results['AP_extreme_tiny']:.3f}\n")
            f.write(f"AP tiny: {results['AP_tiny']:.3f}\n")
            f.write(f"AP small: {results['AP_small']:.3f}\n")
            f.write(f"AP medium: {results['AP_medium']:.3f}\n")
            f.write(f"AP large: {results['AP_large']:.3f}\n\n")

            f.write("Average Recall (AR) Metrics:\n")
            f.write(f"AR1: {results['AR1']:.3f}\n")
            f.write(f"AR10: {results['AR10']:.3f}\n")
            f.write(f"AR100: {results['AR100']:.3f}\n")
            f.write(f"AR extreme tiny: {results['AR_extreme_tiny']:.3f}\n")
            f.write(f"AR tiny: {results['AR_tiny']:.3f}\n")
            f.write(f"AR small: {results['AR_small']:.3f}\n")
            f.write(f"AR medium: {results['AR_medium']:.3f}\n")
            f.write(f"AR large: {results['AR_large']:.3f}\n")

        print(f"Results saved to {output_file}")
