"""
SAFit Evaluation Package
Modified from RGBT-Tiny project for standalone SAFit evaluation

This package provides tools for evaluating object detection models using the SAFit metric,
which is specifically designed for small object detection tasks.

Main components:
- SAFitEvaluator: Main evaluation class
- safit_utils: Utility functions for SAFit calculation
- model_inference: Model inference utilities
- evaluate: Command-line evaluation script
"""

from .safit_evaluator import SAFitEvaluator
from .safit_utils import (
    sigmoid_np, sigmoid_torch, yolo2xyxy_np, xyxy2xywh_np,
    iou_np, nwd_np, safit_np, load_yolo_labels, parse_yolo_predictions
)

__version__ = "1.0.0"
__author__ = "Modified from RGBT-Tiny project"

__all__ = [
    'SAFitEvaluator',
    'sigmoid_np', 'sigmoid_torch', 'yolo2xyxy_np', 'xyxy2xywh_np',
    'iou_np', 'nwd_np', 'safit_np', 'load_yolo_labels', 'parse_yolo_predictions'
]
